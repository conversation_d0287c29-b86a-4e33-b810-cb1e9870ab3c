<?php $__env->startSection('title', config('app.name', 'UniLink')); ?>

<?php $__env->startPush('styles'); ?>
    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::styles(); ?>

<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
    <!-- Defer non-critical JavaScript to prevent layout blocking -->
    <script defer src="<?php echo e(asset('js/post-summary-updater.js')); ?>"></script>
    <script defer src="<?php echo e(asset('js/comments.js')); ?>"></script>
    <script defer src="<?php echo e(asset('js/comment-modal.js')); ?>"></script>
    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::scripts(); ?>

<?php $__env->stopPush(); ?>

<?php $__env->startSection('body-class', ($bodyClass ?? 'bg-gray-100') . ' overflow-hidden'); ?>

<?php $__env->startSection('navigation'); ?>
    <?php echo $__env->make('layouts.unilink-header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('left-sidebar'); ?>
    <!-- Container wrapper to match header max-width -->
    <div class="hidden lg:block fixed left-0 top-16 bottom-0 z-40 w-full pointer-events-none">
        <div class="max-w-screen-2xl mx-auto px-4 sm:px-6 lg:px-8 h-full relative">
            <div class="lg:flex lg:flex-col lg:w-70 xl:w-80 bg-white border-r <?php echo e($leftBorderClass ?? 'border-gray-200'); ?> overflow-y-auto absolute left-4 sm:left-6 lg:left-8 top-0 bottom-0 sidebar-stable pointer-events-auto">
                <?php echo $__env->make('layouts.unilink-left-sidebar-content', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('right-sidebar'); ?>
    <!-- Container wrapper to match header max-width -->
    <div class="hidden lg:block fixed right-0 top-16 bottom-0 z-40 w-full pointer-events-none">
        <div class="max-w-screen-2xl mx-auto px-4 sm:px-6 lg:px-8 h-full relative">
            <div class="lg:flex lg:flex-col lg:w-70 xl:w-80 bg-white border-l border-custom-second-darkest overflow-y-auto absolute right-4 sm:right-6 lg:right-8 top-0 bottom-0 scrollbar-hide sidebar-stable pointer-events-auto">
                <div class="livewire-stable">
                    <?php echo $__env->make('layouts.unilink-right-sidebar-content', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('mobile-sidebar'); ?>
    <!-- Mobile Sidebar -->
    <div class="lg:hidden">
        <?php echo $__env->make('layouts.unilink-sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    </div>

    <!-- Mobile Sidebar Overlay -->
    <div x-data="{ open: false }"
         x-on:toggle-sidebar.window="open = !open"
         x-show="open"
         x-cloak
         x-transition:enter="transition-opacity ease-linear duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition-opacity ease-linear duration-300"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 <?php echo e($overlayClass ?? 'bg-custom-darkest'); ?> bg-opacity-75 z-30 lg:hidden"
         @click="$dispatch('toggle-sidebar')">
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Central Content with proper margins for contained sidebars -->
    <main class="<?php echo e($mainBgClass ?? 'bg-gray-100'); ?> pt-16">
        <div class="central-content-scroll scrollbar-hide">
            <div class="<?php echo e($maxWidthClass ?? 'max-w-screen-2xl'); ?> mx-auto px-4 sm:px-6 lg:px-8 <?php echo e($paddingClass ?? 'py-6'); ?>">
                <div class="lg:ml-70 xl:ml-80 lg:mr-70 xl:mr-80">
                    <!-- Header (if provided) -->
                    <?php if(isset($header)): ?>
                        <div class="mb-6">
                            <?php echo e($header); ?>

                        </div>
                    <?php endif; ?>

                    <!-- Main Content -->
                    <?php if(isset($feedLayout) && $feedLayout): ?>
                        <div class="space-y-4">
                            <?php echo e($slot); ?>

                        </div>
                    <?php else: ?>
                        <?php echo e($slot); ?>

                    <?php endif; ?>
                </div>
            </div>
        </div>
    </main>

    <!-- Notification Popup -->
    <?php echo $__env->make('components.notification-popup', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <!-- Image Modal -->
    <?php echo $__env->make('components.image-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/components/layouts/unilink-layout.blade.php ENDPATH**/ ?>