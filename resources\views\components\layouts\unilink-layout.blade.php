@extends('layouts.app')

@section('title', config('app.name', 'UniLink'))

@push('styles')
    @livewireStyles
@endpush

@push('scripts')
    <!-- Defer non-critical JavaScript to prevent layout blocking -->
    <script defer src="{{ asset('js/post-summary-updater.js') }}"></script>
    <script defer src="{{ asset('js/comments.js') }}"></script>
    <script defer src="{{ asset('js/comment-modal.js') }}"></script>
    @livewireScripts
@endpush

@section('body-class', ($bodyClass ?? 'bg-gray-100') . ' overflow-hidden')

@section('navigation')
    @include('layouts.unilink-header')
@endsection

@section('left-sidebar')
    <!-- Container wrapper to match header max-width -->
    <div class="hidden lg:block fixed left-0 top-16 bottom-0 z-40 w-full pointer-events-none">
        <div class="max-w-screen-2xl mx-auto h-full relative">
            <div class="lg:flex lg:flex-col lg:w-70 xl:w-80 bg-white border-r {{ $leftBorderClass ?? 'border-gray-200' }} overflow-y-auto absolute left-0 top-0 bottom-0 sidebar-stable pointer-events-auto">
                @include('layouts.unilink-left-sidebar-content')
            </div>
        </div>
    </div>
@endsection

@section('right-sidebar')
    <!-- Container wrapper to match header max-width -->
    <div class="hidden lg:block fixed right-0 top-16 bottom-0 z-40 w-full pointer-events-none">
        <div class="max-w-screen-2xl mx-auto h-full relative">
            <div class="lg:flex lg:flex-col lg:w-70 xl:w-80 bg-white border-l border-custom-second-darkest overflow-y-auto absolute right-0 top-0 bottom-0 scrollbar-hide sidebar-stable pointer-events-auto">
                <div class="livewire-stable">
                    @include('layouts.unilink-right-sidebar-content')
                </div>
            </div>
        </div>
    </div>
@endsection

@section('mobile-sidebar')
    <!-- Mobile Sidebar -->
    <div class="lg:hidden">
        @include('layouts.unilink-sidebar')
    </div>

    <!-- Mobile Sidebar Overlay -->
    <div x-data="{ open: false }"
         x-on:toggle-sidebar.window="open = !open"
         x-show="open"
         x-cloak
         x-transition:enter="transition-opacity ease-linear duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition-opacity ease-linear duration-300"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 {{ $overlayClass ?? 'bg-custom-darkest' }} bg-opacity-75 z-30 lg:hidden"
         @click="$dispatch('toggle-sidebar')">
    </div>
@endsection

@section('content')
    <!-- Central Content with proper margins for contained sidebars -->
    <main class="{{ $mainBgClass ?? 'bg-gray-100' }} pt-16">
        <div class="central-content-scroll scrollbar-hide">
            <!-- Container wrapper to match header and sidebars -->
            <div class="{{ $maxWidthClass ?? 'max-w-screen-2xl' }} mx-auto">
                <!-- Content with sidebar margins applied within the container -->
                <div class="lg:ml-70 xl:ml-80 lg:mr-70 xl:mr-80 px-4 sm:px-6 lg:px-8 {{ $paddingClass ?? 'py-6' }}">
                    <!-- Header (if provided) -->
                    @isset($header)
                        <div class="mb-6">
                            {{ $header }}
                        </div>
                    @endisset

                    <!-- Main Content -->
                    @if(isset($feedLayout) && $feedLayout)
                        <div class="space-y-4">
                            {{ $slot }}
                        </div>
                    @else
                        {{ $slot }}
                    @endif
                </div>
            </div>
        </div>
    </main>

    <!-- Notification Popup -->
    @include('components.notification-popup')

    <!-- Image Modal -->
    @include('components.image-modal')
@endsection